<template>
  <div class="mftcc-layout-header">
    <div class="outer-west-logo">
      <img class="layout-logo" style="width: 65px" :src="logo" />
      <span>{{ systemName }}</span>
      <img v-if="showLogoTitle" class="layout-title" :src="logoTitle" />
    </div>
    <el-scrollbar
      style="height: 55px; max-width: calc(100vw - 560px); float: left"
    >
      <div class="rk-menu">
        <el-menu
          mode="horizontal"
          @select="headerMenuFunc"
          class="mftcc-header-menu"
          ref="headerMenu"
          :default-active="activeFirstMenuId"
        >
          <!-- 一级菜单 -->
          <el-menu-item
            v-for="menu in firstLevelMenus"
            :key="menu.id"
            :index="menu.id"
          >
            <span>{{ menu.title }}</span>
          </el-menu-item>
        </el-menu>
      </div>
    </el-scrollbar>

    <!-- 快捷入口按钮 -->
    <div class="middle-north-east" @click="fixedMenu" v-if="pageType != 'set'">
      <img
        v-if="menuIconType === 'image'"
        style="height: 26px; width: 26px; background-size: 100% 100%"
        :src="menuIconImage"
      />
      <i v-else class="el-icon-menu"></i>
    </div>

    <!-- 设置页面的快捷入口（无点击事件） -->
    <div class="middle-north-east" v-if="pageType == 'set'">
      <img
        v-if="menuIconType === 'image'"
        style="height: 26px; width: 26px; background-size: 100% 100%"
        :src="menuIconImage"
      />
      <i v-else class="el-icon-menu"></i>
    </div>

    <!-- 正常页面的头部菜单 -->
    <div class="middle-north-center" v-if="pageType != 'set'">
      <!-- 客户搜索 -->
      <div
        class="head-search-div"
        v-if="$hasPerm('headrSearch')"
        style="width: 175px"
      >
        <input
          v-model="inputValue"
          class="input_content"
          type="text"
          @keyup.enter="searchClick()"
          placeholder="请输入客户名称"
          style="width: 175px"
        />
        <div class="search-icon-div">
          <i class="el-icon-search search-icon-i" @click="searchClick"></i>
        </div>
      </div>

      <!-- 倒计时 -->
      <div class="head-search-div" v-if="showCountdown">
        <div class="count-down-div" style="width: 180px">
          操作倒计时：{{ minute }}分钟
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="请在规定时间内完成操作，以免登录超时造成未提交的信息丢失。"
          >
            <el-button type="text" slot="reference">
              <i class="el-icon-question"></i>
            </el-button>
          </el-popover>
        </div>
      </div>

      <!-- 头部菜单 -->
      <el-menu
        mode="horizontal"
        @select="headerMenuFunc"
        class="mftcc-header-menu"
        ref="headerMenu"
        :default-active="activeFirstMenuId"
      >
        <!-- 驾驶舱 -->
        <el-menu-item index="cockpit" v-if="$hasPerm('head_cockpit')">
          <el-badge class="item menu-notice-num" type="danger">
            <div>
              <i class="el-icon-sunrise-1"></i>
            </div>
          </el-badge>
          <span>驾驶舱</span>
        </el-menu-item>

        <!-- 消息 -->
        <el-menu-item index="notice">
          <el-badge
            :value="noticeNum"
            :max="99"
            class="item menu-notice-num"
            type="danger"
            v-if="noticeNum > 0"
          >
            <div>
              <i class="el-icon-chat-dot-round"></i>
            </div>
          </el-badge>
          <i class="el-icon-chat-dot-round" v-if="noticeNum == 0"></i>
          <span>消息</span>
        </el-menu-item>

        <!-- 待办 -->
        <el-menu-item index="approval">
          <el-badge
            :value="approvalNum"
            class="menu-approval-num"
            type="danger"
            v-if="approvalNum > 0"
          >
            <i class="el-icon-bell"></i>
          </el-badge>
          <i class="el-icon-bell" v-if="approvalNum == 0"></i>
          <span>待办</span>
        </el-menu-item>

        <!-- 用户信息 -->
        <el-menu-item index="user" class="menu-user">
          <el-popover
            placement="bottom-end"
            title=""
            trigger="hover"
            popper-class="menu-user-content"
            :offset="70"
          >
            <div class="menu-user-title" @click="headerMenuFunc('user')">
              <div class="menu-user-message">
                <div>
                  <img :src="userImg" />
                </div>
                <div style="margin-left: 6px">
                  <div class="width-270">
                    <div class="head-username-div">
                      <span>{{ userName }}</span>
                    </div>
                    <div class="codeImage">
                      <img src="/mftcc-web/static/images/home/<USER>/code.png" />
                    </div>
                    <div></div>
                  </div>

                  <div class="head-br-div">
                    <span>{{ brName }}(部门)</span>
                  </div>
                  <div class="head-role-div" style="margin-top: 12px">
                    <div class="img-div">
                      <img
                        src="/mftcc-web/static/images/home/<USER>/privateTel.png"
                      />
                    </div>
                    {{ privateTel }}
                  </div>
                  <div class="head-role-div">
                    <div class="img-div">
                      <img
                        src="/mftcc-web/static/images/home/<USER>/email.png"
                      />
                    </div>
                    {{ email }}
                  </div>
                </div>
              </div>
            </div>
            <div class="menu-user-button">
              <el-button type="primary" @click="updatePassword"
                >修改密码
              </el-button>
              <el-divider direction="vertical"></el-divider>
              <el-button type="primary" @click="signOut">退出系统 </el-button>
            </div>
            <div class="menu-user-info" @click="navRouter" slot="reference">
              <img :src="userImg" />
              <span>欢迎您，{{ userName }}</span>
            </div>
          </el-popover>
        </el-menu-item>
      </el-menu>
    </div>

    <!-- 设置页面的头部菜单（简化版） -->
    <div class="middle-north-center" v-if="pageType == 'set'">
      <el-menu
        mode="horizontal"
        @select="headerMenuFunc"
        class="mftcc-header-menu"
        ref="headerMenu"
      >
        <el-menu-item index="user" class="menu-user">
          <el-popover
            placement="bottom-end"
            title=""
            popper-class="menu-user-content"
            :offset="70"
          >
            <div class="menu-user-info" slot="reference">
              <img :src="userImg" />
              <span>欢迎您，{{ userName }}</span>
            </div>
          </el-popover>
        </el-menu-item>
      </el-menu>
    </div>
  </div>
</template>

<script>
export default {
  name: "LayoutHeader",
  data() {
    return {
      searchShow: false,
      formData: {},
      inputValue: "",
      systemName:
        this.$sys_setting != undefined &&
        this.$sys_setting.system_name != undefined &&
        this.$sys_setting.system_name != ""
          ? this.$sys_setting.system_name
          : "综合业务管理平台",
      logoTitle:
        this.$sys_setting != undefined &&
        this.$sys_setting.logo_title != undefined &&
        this.$sys_setting.logo_title != ""
          ? this.$sys_setting.logo_title
          : "/mftcc-web/static/images/header/logo_title.png",
      logo:
        this.$sys_setting != undefined &&
        this.$sys_setting.logo_img != undefined &&
        this.$sys_setting.logo_img != ""
          ? this.$sys_setting.logo_img
          : "/mftcc-web/static/images/header/logo_wj.png",
      pageType: "home", //从设置菜单路由进来为set
      minute: "", //分
      flag: false,
      firstLevelMenus: [], // 一级菜单数据
      activeFirstMenuId: "", // 当前激活的一级菜单ID
    };
  },
  props: {
    userName: String,
    approvalNum: String,
    noticeNum: Number,
    brName: String,
    phone: String,
    privateTel: String,
    userImg: String,
    roleName: String,
    email: String,
    // 是否显示logo标题图片
    showLogoTitle: {
      type: Boolean,
      default: false,
    },
    // 是否显示倒计时
    showCountdown: {
      type: Boolean,
      default: true,
    },
    // 菜单图标类型：'icon' 或 'image'
    menuIconType: {
      type: String,
      default: "icon",
      validator: (value) => ["icon", "image"].includes(value),
    },
    // 菜单图标图片路径（当 menuIconType 为 'image' 时使用）
    menuIconImage: {
      type: String,
      default: "",
    },
  },
  created() {
    // this.getData();
    // 初始化一级菜单
    this.initFirstLevelMenus();
  },
  mounted() {
    let menuId = sessionStorage.getItem("mId");
    //设置菜单
    if (menuId == "sys_set") {
      this.pageType = "set";
    }

    // 根据当前路由设置一级菜单选中状态
    this.$nextTick(() => {
      this.setActiveFirstMenuByRoute(this.$route.path);
    });

    let time = setInterval(() => {
      if (this.flag == true) {
        clearInterval(time);
      }
      this.timeDown();
    }, 500);
  },
  watch: {
    //监听刷新token是否刷新,如刷新则重置 倒计时 剩余分钟数
    "$store.getters.refreshToken": {
      immediate: true,
      handler(val) {
        const nowTime = new Date();
        let loginExpire = parseInt(this.$store.getters.user.loginExpire);
        console.log("loginExpire:" + loginExpire);
        let loginExpireDateTime = new Date(
          nowTime.setSeconds(nowTime.getSeconds() + loginExpire)
        );
        this.$store.getters.user.loginExpireDateTime = loginExpireDateTime;
      },
    },
  },
  methods: {
    timeDown() {
      if (this.$store.getters.user != null) {
        const endTime = new Date(this.$store.getters.user.loginExpireDateTime);
        const nowTime = new Date();
        let leftTime = parseInt((endTime.getTime() - nowTime.getTime()) / 1000);
        let m = Math.ceil((leftTime / 60) % 60);
        let h = Math.floor((leftTime / 60 / 60) % 24);
        let d = Math.floor(leftTime / 60 / 60 / 24);
        if (h > 0) {
          m = m + h * 60;
        }
        if (d > 0) {
          m = m + d * 24 * 60;
        }
        if (leftTime <= 0) {
          this.flag = true;
          this.$emit("time-end");
          m = 0;
        }
        this.minute = m; //分
      }
    },
    navRouter() {
      this.$router.push({ path: this.$index_router });
    },
    signOut() {
      this.$emit("signOut");
    },
    searchClick() {
      this.$emit("openCusSearch", this.inputValue);
    },
    updatePassword() {
      this.$emit("updatePassword");
    },
    headerMenuFunc(index, indexPath) {
      // 检查是否是一级菜单
      const menuItem = this.firstLevelMenus.find((m) => m.id === index);
      if (menuItem) {
        // 点击了一级菜单，通知侧边栏更新
        this.activeFirstMenuId = index;
        this.$emit("firstMenuClick", menuItem);

        // 如果一级菜单有URL，直接跳转
        if (menuItem.url) {
          this.$router.push(menuItem.url);
        } else if (menuItem.children && menuItem.children.length > 0) {
          // 如果一级菜单没有URL但有子菜单，跳转到第一个有URL的子菜单
          const firstChildWithUrl = this.findFirstMenuWithUrl(
            menuItem.children
          );
          if (firstChildWithUrl) {
            this.$router.push(firstChildWithUrl.url);
          }
        }
      } else {
        // 点击了其他菜单（驾驶舱、消息、待办等）
        this.$emit("cancelSelect", "menu", null);
        this.$emit("menuFunc", index);
        this.menuClick(index);
      }
    },
    // 初始化一级菜单
    initFirstLevelMenus() {
      const allMenus = this.$store.getters.menu || [];
      this.firstLevelMenus = allMenus.map((menu) => ({
        id: menu.id,
        title: menu.title,
        icon: menu.icon,
        url: menu.url, // 添加URL字段
        children: menu.children,
      }));

      // 设置默认激活的一级菜单
      if (this.firstLevelMenus.length > 0) {
        this.activeFirstMenuId = this.firstLevelMenus[0].id;
      }
    },
    // 递归查找第一个有URL的菜单项
    findFirstMenuWithUrl(menuList) {
      for (const menu of menuList) {
        if (menu.url) {
          return menu;
        }
        if (menu.children && menu.children.length > 0) {
          const found = this.findFirstMenuWithUrl(menu.children);
          if (found) {
            return found;
          }
        }
      }
      return null;
    },
    // 根据当前路由查找对应的一级菜单
    findFirstMenuByRoute(currentRoute) {
      const allMenus = this.$store.getters.menu || [];
      for (const firstMenu of allMenus) {
        if (this.isRouteInMenu(currentRoute, firstMenu)) {
          return firstMenu;
        }
      }
      return null;
    },
    // 递归检查路由是否在菜单树中
    isRouteInMenu(route, menu) {
      // 去掉路由参数，只比较路径
      const cleanRoute = route.split("?")[0];
      const cleanMenuUrl = menu.url ? menu.url.split("?")[0] : "";

      if (cleanMenuUrl && cleanRoute === cleanMenuUrl) {
        return true;
      }

      if (menu.children && menu.children.length > 0) {
        for (const child of menu.children) {
          if (this.isRouteInMenu(route, child)) {
            return true;
          }
        }
      }

      return false;
    },
    // 设置一级菜单的激活状态（供外部调用）
    setActiveFirstMenuByRoute(route) {
      const firstMenu = this.findFirstMenuByRoute(route);
      if (firstMenu) {
        this.activeFirstMenuId = firstMenu.id;
        this.$emit("firstMenuClick", firstMenu);
      }
    },
    fixedMenu() {
      this.$emit("fixedMenuFunc");
    },
    cancelSelect(index) {
      this.$refs.headerMenu.activeIndex = index;
    },
    getSelection() {
      return this.$refs.headerMenu.activeIndex;
    },
    //右上菜单点击事件回调
    menuClick(type) {
      switch (type) {
        case "user":
          console.log("用户点击事件");
          break;
        case "search":
          console.log("查询点击事件");
          break;
        case "notice":
          this.$router.push("/msg/detail/msgDetails");
          break;
        case "approval":
          this.$router.push(this.$approve_router);
          break;
        case "cockpit":
          this.$router.push(this.$report_router);
          break;
      }
    },
  },
};
</script>

<style scoped>
.layout-logo {
  height: 52px;
  background-size: 100% 100%;
  width: 182px;
}

.layout-title {
  height: 60%;
  background-size: 100% 100%;
  margin-left: 20px;
  margin-top: 12px;
}

.count-down-div {
  color: #fff;
  height: 55px;
  font-size: 14px;
}

.count-down-div >>> button span {
  color: #fff;
  height: 55px;
  font-size: 14px;
}
</style>

<style scoped src="@/assets/css/newHead.css" />
