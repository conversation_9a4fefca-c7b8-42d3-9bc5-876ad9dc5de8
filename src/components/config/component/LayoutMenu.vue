<template>
  <div class="mftcc-layout-menu" ref="container">
    <el-scrollbar>
      <el-menu
        :default-active="selectStatus"
        :collapse="iscollapse"
        class="menu"
        ref="menu"
        :style="'min-height:' + microAppHeight + 'px'"
        :unique-opened="true"
      >
        <!-- 直接渲染子菜单，不显示一级菜单 -->
        <template v-for="item in menuData">
          <!-- 有子菜单的项 -->
          <el-submenu
            v-if="item.children && item.children.length > 0"
            :key="`submenu-${item.id}`"
            :index="item.id"
          >
            <template slot="title">
              <i :class="item.icon"></i>
              <span>{{ item.title }}</span>
            </template>
            <!-- 递归渲染子菜单 -->
            <menu-item-group
              v-for="child in item.children"
              :key="child.id"
              :menu-item="child"
              :show-menu-bg="showMenuBg"
              @nav-router="navRouterChildren"
            />
          </el-submenu>

          <!-- 没有子菜单的项 -->
          <el-menu-item
            v-else
            :key="`menuitem-${item.id}`"
            :index="item.id"
            @click="navRouterChildren(item)"
          >
            <template slot="title">
              <i :class="item.icon"></i>
              <span slot="title">{{ item.title }}</span>
              <div v-if="showMenuBg" class="menu_bg"></div>
            </template>
          </el-menu-item>
        </template>
      </el-menu>
    </el-scrollbar>
    <transition name="mftcc-multilevel-menu-transition">
      <div v-if="multilevelMenu" class="mftcc-multilevel-menu">
        <el-scrollbar>
          <div
            class="mftcc-multilevel-menu-item"
            v-for="(item, index) in menuItems"
            :key="index"
          >
            <div class="second-menu-title">
              <template
                v-if="item.children !== undefined && item.children.length > 0"
              >
                <span>{{ item.title }}</span>
                <div
                  v-for="menuItem in item.children"
                  :key="menuItem.id"
                  class="three-menu-title"
                  @click="navRouterChildren(menuItem)"
                >
                  <i class="el-icon-arrow-right"></i>
                  {{ menuItem.title }}
                </div>
              </template>
              <template v-else>
                <span @click="navRouterChildren(item)">
                  <i class="el-icon-arrow-right"></i>
                  {{ item.title }}
                </span>
              </template>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </transition>
    <div
      v-if="multilevelMenu"
      @click="maskClick"
      class="mftcc-multilevel-menu-mask"
    ></div>
  </div>
</template>
<script>
// 递归菜单项组件
const MenuItemGroup = {
  name: "MenuItemGroup",
  props: {
    menuItem: {
      type: Object,
      required: true,
    },
    showMenuBg: {
      type: Boolean,
      default: false,
    },
  },
  template: `
    <div>
      <!-- 有子菜单 -->
      <el-submenu v-if="menuItem.children && menuItem.children.length > 0" :index="menuItem.id">
        <template slot="title">
          <i :class="menuItem.icon"></i>
          <span>{{ menuItem.title }}</span>
          <div v-if="showMenuBg" class="menu_bg"></div>
        </template>
        <menu-item-group
          v-for="child in menuItem.children"
          :key="child.id"
          :menu-item="child"
          :show-menu-bg="showMenuBg"
          @nav-router="handleNavRouter"
        />
      </el-submenu>

      <!-- 没有子菜单 -->
      <el-menu-item v-else :index="menuItem.id" @click="handleNavRouter(menuItem)">
        <i :class="menuItem.icon"></i>
        <span>{{ menuItem.title }}</span>
        <div v-if="showMenuBg" class="menu_bg"></div>
      </el-menu-item>
    </div>
  `,
  methods: {
    handleNavRouter(item) {
      this.$emit("nav-router", item);
    },
  },
};

export default {
  name: "LayoutMenu",
  inject: ["reload"],
  components: {
    MenuItemGroup,
  },
  data() {
    return {
      menuData: [],
      multilevelMenu: false,
      clickIndex: null,
      activeIndex: null,
      menuItems: [],
      headerMenuIndex: null,
      selectStatus: "",
      microAppHeight: 0,
      menuShowType: "1", //二级及其下菜单展示方式1-分级菜单；2-信息块
    };
  },
  props: {
    iscollapse: {
      type: Boolean,
      default: false,
    },
    // 是否显示菜单背景图（blue主题使用）
    showMenuBg: {
      type: Boolean,
      default: false,
    },
    // URL处理模式：'default' 或 'blue'
    urlMode: {
      type: String,
      default: "blue",
      validator: (value) => ["default", "blue"].includes(value),
    },
    // 当前激活的一级菜单（从header传入）
    activeFirstMenu: {
      type: Object,
      default: null,
    },
  },
  created() {
    // this.initMenu();
    let data = this.menuData;
    this.selectStatus = this.getParentNode(this.$route.fullPath, data);
    window.$menu = this;
    //获取二级及其下菜单展示方式
    let dicKeyArray = ["PLT_MENU_SHOW_TYPE"];
    this.$formUtil.getParmDic(dicKeyArray, (dicKeyData) => {
      let dic = dicKeyData.PLT_MENU_SHOW_TYPE;
      // 使用安全的字典访问方法
      this.menuShowType = this.$dicUtil
        ? this.$dicUtil.safeGetFirstOptCode(dic, "1", "PLT_MENU_SHOW_TYPE")
        : dic && dic.length > 0 && dic[0] && dic[0].optCode
        ? dic[0].optCode
        : "1";
    });
  },
  mounted() {
    /* 如果是新页签则默认打开第一个菜单的路由 */
    if (this.$route.query.menuId) {
      let menuData = this.menuData;
      for (let i = 0; i < menuData.length; i++) {
        if (!menuData[i].children && menuData[i].url) {
          this.navRouter(menuData[i]);
          break;
        } else if (menuData[i].children) {
          if (this.getChildNode(menuData[i].children)) {
            break;
          }
        }
      }
    }
    this.microAppHeight = document.body.clientHeight - 55;
  },
  watch: {
    // 监听一级菜单变化，更新侧边栏显示的子菜单
    activeFirstMenu: {
      handler(newMenu) {
        console.log("🚗 🚗 🚗 ~ handler ~ newMenu:", newMenu);
        if (newMenu && newMenu.children) {
          this.menuData = newMenu.children;
        } else if (newMenu && !newMenu.children) {
          this.menuData = [];
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    /* 根据菜单ID设置菜单选中的效果，给业务使用 */
    setMenuStatus(menuId) {
      this.selectStatus = menuId;
    },
    navRouter(item) {
      if (this.menuShowType == "1") {
        let url = item.url;
        // 根据不同模式处理URL
        if (this.urlMode === "default" && url) {
          url = "/" + $productName + url;
        }
        let children = item.children;
        let newTab = item.newTab;
        this.$emit("getSelection", "headerMenu", (headerMenuIndex) => {
          if (headerMenuIndex !== null) {
            this.headerMenuIndex = headerMenuIndex;
          }
        });
        if (newTab == 0 && children != undefined) {
          let routeUrl = this.$router.resolve({
            path: "/main", //新页面地址
            query: { menuId: item.id }, //携带的参数
          });
          window.open(routeUrl.href);
          this.multilevelMenu = false;
          return;
        } else if (url == "" && children == undefined) {
          this.multilevelMenu = false;
          return;
        }
        this.$emit("cancelSelect", "headerMenu", null);
        if (url && children == undefined && newTab != 0) {
          this.multilevelMenu = false;
          if (
            this.urlMode === "default" &&
            typeof singleSpaNavigate !== "undefined"
          ) {
            singleSpaNavigate(url);
          } else {
            this.$router.push(url);
          }
          this.activeIndex = this.$refs.menu.activeIndex;
          this.headerMenuIndex = null;
          if (this.urlMode === "default") {
            this.selectStatus = this.getParentNode(url, this.menuData);
          }
          this.reload();
        } else if (
          url &&
          children == undefined &&
          newTab == 0 &&
          this.$route.query.menuId
        ) {
          this.multilevelMenu = false;
          if (typeof singleSpaNavigate !== "undefined") {
            singleSpaNavigate(url);
          }
          this.activeIndex = this.$refs.menu.activeIndex;
          this.reload();
        } else if (
          url &&
          children == undefined &&
          newTab == 0 &&
          !this.$route.query.menuId
        ) {
          this.multilevelMenu = false;
          return;
        } else if (this.multilevelMenu === true) {
          if (this.clickIndex !== item.id) {
            this.multilevelMenu = false;
            this.menuItems = children;
            this.$nextTick(() => {
              setTimeout(() => {
                this.multilevelMenu = true;
              }, 300);
            });
          } else {
            this.maskClick();
          }
        } else {
          this.menuItems = children;
          this.multilevelMenu = true;
        }
        this.clickIndex = this.$refs.menu.activeIndex;
        sessionStorage.setItem("currentRouter", item.url);
      }
    },
    navRouterChildren(item) {
      this.multilevelMenu = false;
      if (
        this.urlMode === "default" &&
        typeof singleSpaNavigate !== "undefined"
      ) {
        singleSpaNavigate(item.url);
      } else {
        this.$router.push(item.url);
      }
      this.activeIndex = this.$refs.menu.activeIndex;
      this.headerMenuIndex = null;
      this.reload();
      sessionStorage.setItem("currentRouter", item.url);
    },
    // initMenu() {
    //   // 如果有传入的一级菜单，直接使用其子菜单
    //   if (this.activeFirstMenu) {
    //     if (this.activeFirstMenu.children) {
    //       this.menuData = this.activeFirstMenu.children;
    //     } else {
    //       this.menuData = []; // 没有子菜单时，侧边栏为空
    //     }
    //     return;
    //   }

    //   let menuId = sessionStorage.getItem("mId");
    //   let menuData = this.$store.getters.menu;
    //   if (this.$route.query.menuId) {
    //     let mId = this.$route.query.menuId;
    //     sessionStorage.setItem("mId", mId);
    //     this.getMenuData(mId, menuData);
    //   } else if (menuId) {
    //     this.getMenuData(menuId, menuData);
    //   } else {
    //     // 默认显示第一个一级菜单的子菜单（不显示一级菜单本身）
    //     if (menuData && menuData.length > 0) {
    //       if (menuData[0].children && menuData[0].children.length > 0) {
    //         this.menuData = menuData[0].children;
    //       } else {
    //         this.menuData = []; // 第一个一级菜单没有子菜单时，侧边栏为空
    //       }
    //     } else {
    //       this.menuData = [];
    //     }
    //   }
    // },
    getMenuData(mId, menuData) {
      menuData.forEach((item) => {
        if (item.id == mId) {
          this.menuData = item.children;
        } else if (item.children) {
          item.children.forEach((item1) => {
            if (item1.id == mId) {
              this.menuData = [];
              this.menuData = item1.children;
            } else {
              if (item1.children != undefined) {
                this.getMenuData(mId, item1.children);
              }
            }
          });
        }
      });
    },
    getParentNode(url, data) {
      url = url.split("?")[0];
      for (let index in data) {
        if (data[index].url == url) {
          return data[index].id;
        } else if (data[index].children) {
          let data1 = data[index].children;
          for (let inx in data1) {
            if (data1[inx].url == url) {
              return data1[inx].id;
            } else {
              if (data1[inx].children != undefined) {
                this.getMenuData(url, data1[inx].children);
              }
            }
          }
        }
      }
    },
    getChildNode(menuData) {
      for (let i = 0; i < menuData.length; i++) {
        if (!menuData[i].children && menuData[i].url) {
          this.navRouter(menuData[i]);
          return true;
        } else if (menuData[i].children) {
          if (this.getChildNode(menuData[i].children)) {
            return true;
          }
        }
      }
    },
    cancelSelect() {
      this.multilevelMenu = false;
      this.$refs.menu.activeIndex = null;
      this.clickIndex = null;
      this.activeIndex = null;
    },
    maskClick() {
      this.multilevelMenu = false;
      this.$refs.menu.activeIndex = this.activeIndex;
      this.clickIndex = this.activeIndex;
      this.$emit("cancelSelect", "headerMenu", this.headerMenuIndex);
    },
  },
};
</script>
