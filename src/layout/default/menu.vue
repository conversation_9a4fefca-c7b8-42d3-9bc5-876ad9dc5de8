<template>
  <layout-menu
    :iscollapse="iscollapse"
    :show-menu-bg="false"
    url-mode="default"
    :active-first-menu="activeFirstMenu"
    @cancelSelect="handleCancelSelect"
    @getSelection="handleGetSelection"
    @updateFirstMenuActive="handleUpdateFirstMenuActive"
    ref="menuComponent"
  />
</template>
<script>
import LayoutMenu from "@/components/config/component/LayoutMenu.vue";

export default {
  components: {
    LayoutMenu,
  },
  props: {
    iscollapse: Boolean,
    activeFirstMenu: Object,
  },
  methods: {
    handleCancelSelect(type, value) {
      this.$emit("cancelSelect", type, value);
    },
    handleGetSelection(type, callback) {
      this.$emit("getSelection", type, callback);
    },
    handleUpdateFirstMenuActive(url) {
      this.$emit("updateFirstMenuActive", url);
    },
    // 暴露给外部调用的方法
    setMenuStatus(menuId) {
      if (this.$refs.menuComponent) {
        this.$refs.menuComponent.setMenuStatus(menuId);
      }
    },
    cancelSelect() {
      if (this.$refs.menuComponent) {
        this.$refs.menuComponent.cancelSelect();
      }
    },
  },
};
</script>
