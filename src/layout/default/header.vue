<template>
  <layout-header
    :userName="userName"
    :approvalNum="approvalNum"
    :noticeNum="noticeNum"
    :brName="brName"
    :phone="phone"
    :privateTel="privateTel"
    :userImg="userImg"
    :roleName="roleName"
    :email="email"
    :show-logo-title="true"
    :show-countdown="true"
    menu-icon-type="icon"
    @signOut="handleSignOut"
    @openCusSearch="handleOpenCusSearch"
    @updatePassword="handleUpdatePassword"
    @cancelSelect="handleCancelSelect"
    @menuFunc="handleMenuFunc"
    @fixedMenuFunc="handleFixedMenuFunc"
    @time-end="handleTimeEnd"
    @firstMenuClick="handleFirstMenuClick"
    ref="headerComponent"
  />
</template>

<script>
import LayoutHeader from "@/components/config/component/LayoutHeader.vue";

export default {
  components: {
    LayoutHeader,
  },
  props: {
    userName: String,
    approvalNum: String,
    noticeNum: Number,
    brName: String,
    phone: String,
    privateTel: String,
    userImg: String,
    roleName: String,
    email: String,
  },
  methods: {
    handleSignOut() {
      this.$emit("signOut");
    },
    handleOpenCusSearch(value) {
      this.$emit("openCusSearch", value);
    },
    handleUpdatePassword() {
      this.$emit("updatePassword");
    },
    handleCancelSelect(type, value) {
      this.$emit("cancelSelect", type, value);
    },
    handleMenuFunc(index) {
      this.$emit("menuFunc", index);
    },
    handleFixedMenuFunc() {
      this.$emit("fixedMenuFunc");
    },
    handleTimeEnd() {
      this.$emit("time-end");
    },
    handleFirstMenuClick(menuItem) {
      this.$emit("firstMenuClick", menuItem);
    },
    // 暴露给外部调用的方法
    cancelSelect(index) {
      if (this.$refs.headerComponent) {
        this.$refs.headerComponent.cancelSelect(index);
      }
    },
    getSelection() {
      if (this.$refs.headerComponent) {
        return this.$refs.headerComponent.getSelection();
      }
      return null;
    },
    setActiveFirstMenuByRoute(route) {
      if (this.$refs.headerComponent) {
        this.$refs.headerComponent.setActiveFirstMenuByRoute(route);
      }
    },
  },
};
</script>
