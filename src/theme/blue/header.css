/* 头部背景色 */
.mftcc-layout-header {
  background: #409eff;
}
/* 头部右侧功能按钮背景色 */
.mftcc-layout-header .middle-north-east {
  background: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 58px;
}
.mftcc-layout-header .el-badge__content.is-fixed {
  background: #d12c2c;
}
.mftcc-layout-header .mftcc-header-menu .el-menu-item:focus,
.mftcc-layout-header .mftcc-header-menu .el-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
.mftcc-layout-header .mftcc-header-menu .el-menu-item.is-active {
  background-color: rgba(0, 0, 0, 0.1);
}
.menu-user-content .menu-user-button {
  background-color: #409eff;
  height: 55px;
}
.menu-user-content .menu-user-button .el-divider {
  height: 40px;
}
.menu-user-content .menu-user-button button {
  background-color: transparent !important;
}
