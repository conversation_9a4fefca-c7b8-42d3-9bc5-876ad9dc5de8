.mftcc-layout-header {
  width: 100%;
  height: 100%;
  background: #409eff;
}
.mftcc-layout-header .middle-north-center {
  height: 55px;
  float: right;
}
.mftcc-layout-header .outer-west-logo {
  height: 100%;
  float: left;
  display: flex;
  padding: 0 20px;
  align-items: center;
}
.mftcc-layout-header .middle-north-east {
  cursor: pointer;
  width: 80px;
  float: right;
  background-color: #fff3;
  height: 55px;
}
.mftcc-layout-header .middle-north-east i {
  font-size: 24px;
  color: white;
  padding: 15px 28px !important;
}
.mftcc-layout-header .mftcc-header-menu {
  float: right;
  background-color: transparent;
  height: 55px;
  border-bottom: none;
}
.mftcc-layout-header .mftcc-header-menu .el-menu-item {
  color: #fff;
  height: 55px;
  font-size: 14px;
  line-height: 25px;
  padding: 10px 15px;
  padding-top: 15px;
}
.mftcc-layout-header .mftcc-header-menu .el-menu-item:focus,
.mftcc-layout-header .mftcc-header-menu .el-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
}
.mftcc-layout-header .mftcc-header-menu .el-menu-item.is-active {
  background-color: rgba(0, 0, 0, 0.2);
  color: #fff;
  border-bottom: none;
}
.mftcc-layout-header .mftcc-header-menu .el-menu-item i {
  float: left;
  width: 20px;
  height: 20px;
  margin-top: 4px;
  margin-right: 5px;
  font-size: 20px;
  color: #fff;
}
.mftcc-layout-header .mftcc-header-menu .menu-user {
  min-width: 208px;
  padding: 0px 15px;
}
.mftcc-layout-header .mftcc-header-menu .menu-user .menu-user-info {
  height: 100%;
  width: 100%;
  display: flex;
}
.mftcc-layout-header .mftcc-header-menu .menu-user .menu-user-info img {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  margin-top: 10px;
  margin-right: 12px;
}
.mftcc-layout-header .mftcc-header-menu .menu-user .menu-user-info span {
  margin-top: 15px;
}
.menu-user-content {
  padding: 0;
  background-color: #ffffff;
  width: 385px;
}
.menu-user-content img {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  cursor: pointer;
  color: #2a2a2a;
}
.menu-user-content .menu-user-title {
  padding: 20px;
}
.menu-user-content .menu-user-title p {
  color: #fff;
  margin: 0px 0 16px 0;
  display: inline-block;
}
/* .menu-user-content .popper__arrow {
  display: none;
} */
.menu-user-content .menu-user-title .menu-user-message {
  display: inline-block;
  width: 100%;
}
.menu-user-content .menu-user-title .menu-user-message div:first-child {
  width: 30%;
  float: left;
}
.menu-user-content .menu-user-title .menu-user-message div:last-child {
  width: 65%;
  float: left;
}
.menu-user-content .menu-user-button {
  width: 100%;
  background-color: #409eff;
  height: 45px;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
}
.menu-user-content .menu-user-button button {
  background-color: #409eff !important;
  font-size: 16px;
  width: calc(50% - 1px) !important;
  float: left;
  border: none;
  margin-left: 0 !important;
  height: 100% !important;
  padding: 0;
}
.menu-user-content .menu-user-button button:hover {
  background-color: #353535;
}
.menu-user-content .menu-user-button .el-divider {
  margin: 0;
  width: 2px;
  height: 33px;
  top: 7px;
  float: left;
  background-color: #dcdfe652;
}
.mftcc-layout-header .mftcc-header-menu .menu-approval {
  min-width: 120px;
}
.mftcc-layout-header .mftcc-header-menu .menu-approval .menu-approval-num {
  float: left;
}
.mftcc-layout-header .mftcc-header-menu .menu-approval sup,
.mftcc-layout-header .mftcc-header-menu .menu-notice sup {
  background-color: #e14a47;
  color: #fff;
  right: 5px;
  top: 5px;
  height: 16px;
  padding: 0px 5px;
  border: none;
  box-shadow: none;
  text-align: center;
  z-index: 99;
}

.rk-menu {
  height: 55px;
}
.rk-menu .el-menu {
  display: flex;
  float: none !important;
}
.rk-menu .el-menu-item {
  border: none !important;
}
.rk-menu .el-menu-item:hover,
.rk-menu .el-menu-item.is-active {
  border: none !important;
  background-color: rgba(0, 0, 0, 0.1) !important;
}
